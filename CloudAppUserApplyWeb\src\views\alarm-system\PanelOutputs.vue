<template>
  <div class="panel-outputs">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- 循环渲染DSC区域 -->
      <template v-if="dscGroups.length">
        <dsc-group
          v-for="(dscGroup, groupIndex) in dscGroups"
          :key="`dsc-group-${groupIndex}`"
          :dsc-group="dscGroup"
          :group-index="groupIndex"
          :get-container="getContainer"
          @output-action="handleOutputActionEvent"
        />
      </template>
      <!-- 空状态 -->
      <empty-state v-else />
    </div>
    <!-- 底部导航组件 -->
    <alarm-bottom-navigation :init-active-index="2" />

    <!-- 编辑输出名称弹窗 -->
    <output-edit-dialog :show.sync="showEditDialog" :current-output="currentEditOutput" @confirm="handleEditConfirm" />
  </div>
</template>

<script>
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'
import EmptyState from './components/EmptyState.vue'
import DscGroup from './components/DscGroup.vue'
import OutputEditDialog from './components/OutputEditDialog.vue'
import { mapGetters, mapActions } from 'vuex'
import { debounce } from 'lodash'

// 开发环境模拟数据
const MOCK_DSC_GROUPS = [
  {
    id: 1,
    outputs: [
      {
        id: 1,
        name: 'output 1001',
        status: 'Off',
        isOn: false,
        showPopover: false
      },
      {
        id: 2,
        name: 'output 1002',
        status: 'Off',
        isOn: false,
        showPopover: false
      },
      {
        id: 3,
        name: 'output 1003',
        status: 'Off',
        isOn: false,
        showPopover: false
      }
    ]
  },
  {
    id: 2,
    outputs: [
      {
        id: 4,
        name: 'output 1004',
        status: 'Off',
        isOn: false,
        showPopover: false
      },
      {
        id: 5,
        name: 'output 1005',
        status: 'Off',
        isOn: false,
        showPopover: false
      }
    ]
  }
]

export default {
  name: 'PanelOutputs',
  components: {
    AlarmBottomNavigation,
    EmptyState,
    DscGroup,
    OutputEditDialog
  },
  data() {
    return {
      showEditDialog: false,
      currentEditOutput: null,
      containerElement: null
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['outputsList', 'systemType', 'canFetchPanelState']),

    // 将outputs数据转换为DSC分组格式
    dscGroups() {
      // 开发环境下使用模拟数据
      if (process.env.NODE_ENV === 'development') {
        return MOCK_DSC_GROUPS
      }

      if (!this.outputsList.length) return []

      return this.groupOutputsBySize(this.outputsList)
    }
  },
  watch: {
    editOutputName: {
      handler(newVal) {
        // 当输入内容时，清除空值错误状态
        if (newVal && this.hasNameEmptyError) {
          this.editOutputNameError = ''
          this.hasNameEmptyError = false
        }
      }
    }
  },
  created() {
    this.loadOutputs()
  },
  mounted() {
    this.containerElement = document.querySelector('.panel-outputs')
  },
  methods: {
    ...mapActions('alarmSystem', ['fetchOutputs', 'setOutputStatus']),

    getContainer() {
      return this.containerElement || document.querySelector('.panel-outputs')
    },

    // 公共的loading处理方法
    async withLoading(asyncFn) {
      try {
        this.$loading.show()
        return await asyncFn()
      } finally {
        this.$loading.hide()
      }
    },

    // 处理来自子组件的输出操作事件（使用防抖）
    handleOutputActionEvent: debounce(function (payload) {
      const { output, action } = payload

      if (action === 'rename') {
        this.handleRename(output)
      } else {
        this.handleOutputAction(output, action)
      }
    }, 300),

    // 加载outputs数据
    async loadOutputs(force = false) {
      if (!this.canFetchPanelState) return

      try {
        await this.withLoading(async () => {
          await this.fetchOutputs({ force })
        })
      } catch (error) {
        this.handleApiError(error)
      }
    },

    // 将outputs数据按大小分组
    groupOutputsBySize(outputs) {
      if (!outputs.length) return []

      const groupSize = 6
      const groups = []

      for (let i = 0; i < outputs.length; i += groupSize) {
        groups.push({
          id: Math.floor(i / groupSize) + 1,
          outputs: outputs.slice(i, i + groupSize)
        })
      }

      return groups
    },
    // 处理输出操作（从下拉菜单）
    async handleOutputAction(output, action) {
      this.closePopover(output)

      const isActive = action === 'on'
      const actionText = isActive ? this.$t('on') : this.$t('off')

      try {
        await this.withLoading(async () => {
          await this.setOutputStatus({
            outputId: output.id,
            isActive: isActive
          })
        })

        this.$toast.success(`${output.name} ${actionText}`)
      } catch (error) {
        this.handleApiError(error)
        this.$toast.fail(this.$t('operationFailedTryAgain'))
      }
    },

    // 关闭popover的公共方法
    closePopover(output) {
      output.showPopover = false
    },
    // 重命名输出
    handleRename(output) {
      this.closePopover(output)
      this.currentEditOutput = output
      this.showEditDialog = true
    },

    // 处理编辑确认事件
    async handleEditConfirm(payload) {
      const { output, newName, done } = payload

      try {
        await this.performUpdateOutput(output, newName)
        done(true)
        this.clearEditDialogState()
      } catch (error) {
        done(false)
      }
    },

    // 清除编辑弹窗状态
    clearEditDialogState() {
      this.currentEditOutput = null
    },

    // 输出更新主入口
    async performUpdateOutput(output, newName) {
      try {
        await this.withLoading(async () => {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1000))
          output.name = newName
        })

        this.$toast.success(this.$t('outputNameUpdatedSuccessfully'))
      } catch (error) {
        this.handleApiError(error)
        this.$toast.fail(this.$t('updateFailedTryAgain'))
        throw error
      }
    },
    // 统一的API错误处理
    handleApiError(error) {
      let errorMessage = this.$t('loadFailedTryAgain')

      if (error.response) {
        switch (error.response.status) {
          case 401:
            errorMessage = this.$t('authenticationExpired')
            break
          case 403:
            errorMessage = this.$t('accessDenied')
            break
          case 404:
            errorMessage = this.$t('resourceNotFound')
            break
          case 500:
            errorMessage = this.$t('serverError')
            break
        }
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = this.$t('networkError')
      }

      this.$toast.fail(errorMessage)
      console.error('Outputs API Error:', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-outputs {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 0;
    gap: 6px;
  }
  .dsc-title {
    height: 44px;
    box-sizing: border-box;
    font-size: 18px;
    font-weight: bold;
    padding: 10px 16px;
  }
  .output-section {
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    min-height: auto;
    padding-bottom: 8px;
    .section-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 16px;
    }
  }
  :deep(.van-popover) {
    .van-popover__arrow {
      display: none;
    }
    .van-popover__content {
      border-radius: 2px;
    }
  }
}
</style>
