<template>
  <div class="alarm-bottom-box output-device-item">
    <div class="device-icon">
      <theme-image imageName="alarm-system/outputs_light.png" :alt="output.name" class="output-icon" />
    </div>
    <div class="device-info">
      <div class="device-name text-over-ellipsis">{{ output.name }}</div>
      <div class="alarm-sub-text device-status">{{ output.status }}</div>
    </div>
    <div class="device-actions">
      <van-popover
        v-model="output.showPopover"
        placement="bottom-end"
        :offset="[-10, 0]"
        trigger="click"
        :get-container="getContainer"
      >
        <ul class="action-btn">
          <li @click="handleAction('off')">{{ $t('off') }}</li>
          <li @click="handleAction('on')">{{ $t('on') }}</li>
          <li @click="handleAction('rename')">{{ $t('rename') }}</li>
        </ul>
        <template #reference>
          <div class="power-switch">
            <theme-image
              :imageName="output.isOn ? 'alarm-system/outputs_on.png' : 'alarm-system/outputs_off.png'"
              class="output-icon"
            />
          </div>
        </template>
      </van-popover>
    </div>
  </div>
</template>

<script>
import { debounce } from 'lodash'

export default {
  name: 'OutputDevice',
  props: {
    output: {
      type: Object,
      required: true,
      validator(value) {
        return (
          value && typeof value.id !== 'undefined' && typeof value.name === 'string' && typeof value.isOn === 'boolean'
        )
      }
    },
    getContainer: {
      type: Function,
      required: true
    }
  },
  emits: ['action'],
  methods: {
    // 使用防抖处理，避免重复点击
    handleAction: debounce(function (action) {
      this.$emit('action', {
        output: this.output,
        action: action
      })
    }, 300)
  }
}
</script>

<style lang="scss" scoped>
.output-device-item {
  height: 64px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  padding: 0px 20px 0px 16px;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .device-icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    .output-icon {
      width: 32px;
      height: 32px;
    }
  }

  .device-info {
    flex: 1;
    min-width: 0;

    .device-name {
      width: 100%;
      height: 22px;
      line-height: 22px;
      font-size: 14px;
    }

    .device-status {
      height: 22px;
      line-height: 22px;
      font-size: 12px;
    }
  }

  .device-actions {
    display: flex;
    align-items: center;

    .power-switch {
      width: 24px;
      height: 24px;
      cursor: pointer;
      padding: 4px;
    }
  }
}

.action-btn {
  width: 190px;
  font-size: 16px;
  font-weight: 400;
  line-height: 22px;

  li {
    padding: 12px 16px;
    cursor: pointer;

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>
