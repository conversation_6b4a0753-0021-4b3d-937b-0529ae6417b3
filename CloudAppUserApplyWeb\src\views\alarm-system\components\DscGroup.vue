<template>
  <div class="panel-outputs-box">
    <!-- DSC 标题 -->
    <div class="dsc-title">{{ $t('dscTitle', 'DSC') }}</div>
    <!-- DSC输出区域 -->
    <div class="alarm-box-wrapper output-section">
      <div class="output-devices">
        <output-device
          v-for="(output, index) in dscGroup.outputs"
          :key="`dsc${groupIndex}-${index}`"
          :output="output"
          :get-container="getContainer"
          @action="handleOutputAction"
        />
      </div>
    </div>
  </div>
</template>

<script>
import OutputDevice from './OutputDevice.vue'

export default {
  name: 'DscGroup',
  components: {
    OutputDevice
  },
  props: {
    dscGroup: {
      type: Object,
      required: true,
      validator(value) {
        return value && Array.isArray(value.outputs) && typeof value.id !== 'undefined'
      }
    },
    groupIndex: {
      type: Number,
      required: true
    },
    getContainer: {
      type: Function,
      required: true
    }
  },
  emits: ['output-action'],
  methods: {
    handleOutputAction(payload) {
      this.$emit('output-action', payload)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-outputs-box {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.dsc-title {
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  padding: 0 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e5e5e5;
}

.alarm-box-wrapper {
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;

  &.output-section {
    border: 1px solid #e5e5e5;
  }
}

.output-devices {
  .output-device-item {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }
}
</style>
