<template>
  <van-dialog
    v-model="visible"
    :title="$t('enterOutputName')"
    :show-cancel-button="true"
    :cancel-button-text="$t('cancel')"
    :confirm-button-text="$t('ok')"
    :before-close="handleBeforeClose"
    @opened="handleDialogOpened"
    class="common-dialog"
  >
    <div class="common-dialog-content">
      <common-input
        v-model="outputName"
        :placeholder="$t('enterOutputName')"
        :errorMessage="errorMessage"
        borderType="full"
        ref="editField"
      />
    </div>
  </van-dialog>
</template>

<script>
import CommonInput from './CommonInput.vue'

export default {
  name: 'OutputEditDialog',
  components: {
    CommonInput
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentOutput: {
      type: Object,
      default: null
    }
  },
  emits: ['update:show', 'confirm'],
  data() {
    return {
      outputName: '',
      errorMessage: '',
      hasNameEmptyError: false
    }
  },
  computed: {
    visible: {
      get() {
        return this.show
      },
      set(value) {
        this.$emit('update:show', value)
      }
    }
  },
  watch: {
    currentOutput: {
      handler(newOutput) {
        if (newOutput) {
          this.outputName = newOutput.name || ''
          this.clearError()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 验证输出名称
    validateOutputName() {
      this.clearError()

      const trimmedName = (this.outputName || '').trim()
      if (!trimmedName) {
        this.errorMessage = this.$t('outputNameCannotBeEmpty')
        this.hasNameEmptyError = true
        return false
      }
      return true
    },

    // 清除错误状态
    clearError() {
      this.errorMessage = ''
      this.hasNameEmptyError = false
    },

    // 弹窗关闭前处理
    async handleBeforeClose(action, done) {
      if (action === 'confirm') {
        if (!this.validateOutputName()) {
          done(false)
          return
        }

        try {
          const trimmedName = (this.outputName || '').trim()
          await this.$emit('confirm', {
            output: this.currentOutput,
            newName: trimmedName,
            done
          })
        } catch (error) {
          done(false)
        }
      } else {
        this.clearState()
        done(true)
      }
    },

    // 弹窗打开后处理
    handleDialogOpened() {
      this.$nextTick(() => {
        if (this.$refs.editField) {
          this.$refs.editField.focus()
        }
      })
    },

    // 清除状态
    clearState() {
      this.outputName = ''
      this.clearError()
    }
  }
}
</script>

<style lang="scss" scoped>
.common-dialog {
  :deep(.van-dialog__header) {
    padding: 20px 20px 16px 20px !important;
  }

  :deep(.van-dialog__content) {
    padding: 0 20px 20px 20px !important;
  }
}
</style>
